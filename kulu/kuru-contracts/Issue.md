in KuruAMMVault

Description: When not nullifying (partial <= new size), updateVaultOrdSz sets new sizes, assuming OrderBook can amend orders (increase if new > partial, reduce if new < partial—but code only nullifies for new < partial). Unmentioned transition: Withdrawing with partial < new < old (after some fills), sets new > partial (increase) or handles reduce implicitly.
Discrepancy: Docs assume seamless size updates; if OrderBook doesn't support amends (common in CLOBs), fails or reverts. Unmentioned if increases require nullify/replace.
Impact: Failed withdrawals if OrderBook rejects (DoS); liquidity stuck.
Recommendation: Always nullify if new != current partial; confirm OrderBook behavior.