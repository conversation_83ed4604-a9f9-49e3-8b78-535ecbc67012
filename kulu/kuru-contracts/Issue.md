in KuruAMMVault

“locked liquidity” minted to a live account (not burned).
First deposit mints MIN_LIQUIDITY shares to marginAccount, not to address(0) (or burned). That account can later call withdraw(MIN_LIQUIDITY, …) and skim a perpetual fraction of the pool. Fix: mint MIN_LIQUIDITY to address(0) (burn) or _mint(address(this), MIN_LIQUIDITY); _burn(address(this), MIN_LIQUIDITY);.
Code: in _mintAndDeposit(...)

## POC Status: VERIFIED - VULNERABILITY EXISTS
The vulnerability has been confirmed through comprehensive testing. The marginAccount can indeed withdraw MIN_LIQUIDITY shares and extract value from the pool perpetually.